<?php
session_start();
require("../database.php");
require_once __DIR__ . '/MAILER/vendor/autoload.php';
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

// Check if user is logged in and has appropriate permissions
$adminisLoggedIn = isset($_SESSION['profile_email']);
if (!$adminisLoggedIn) {
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Unauthorized access']));
}

// Get parameters
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$round_number = isset($_GET['round_number']) ? $_GET['round_number'] : '';
$flight_list_name = isset($_GET['flight_list_name']) ? $_GET['flight_list_name'] : '';

if (!$event_mgm_id || !$round_number || !$flight_list_name) {
    header('Content-Type: application/json');
    die(json_encode(['error' => 'Missing required parameters']));
}

// --- Fetch Event Name ---
$event_info_query = "
    SELECT event_name
    FROM event_mgm
    WHERE event_mgm_id = ?
    LIMIT 1";
$stmt_event_info = $conn->prepare($event_info_query);
$stmt_event_info->bind_param("s", $event_mgm_id);
$stmt_event_info->execute();
$event_info_result = $stmt_event_info->get_result();
$event_info_row = $event_info_result->fetch_assoc();
$event_name = $event_info_row['event_name'] ?? 'Unknown Event';
$stmt_event_info->close();

// First, get the event_ids associated with event_mgm_id
$event_query = "
    SELECT event_id 
    FROM event_mgm 
    WHERE event_mgm_id = ?";
$stmt_event = $conn->prepare($event_query);
if (!$stmt_event) {
    error_log("Failed to prepare event query: " . $conn->error);
    die("Failed to prepare event query");
}
$stmt_event->bind_param("s", $event_mgm_id);
if (!$stmt_event->execute()) {
    error_log("Failed to execute event query: " . $stmt_event->error);
    die("Failed to execute event query");
}
$event_result = $stmt_event->get_result();

$event_ids = [];
while ($row = $event_result->fetch_assoc()) {
    $event_ids[] = $row['event_id'];
}
$stmt_event->close();

if (empty($event_ids)) {
    error_log("No events found for event_mgm_id: " . $event_mgm_id);
    die('No events found for this event_mgm_id');
}

// Create placeholders for IN clause
$placeholders = str_repeat('?,', count($event_ids) - 1) . '?';
$types_events = str_repeat('s', count($event_ids));

    // --- Fetch Scorecard Configurations for this event ---
    $scorecard_query = "
        SELECT sc.scorecard_id, sc.name, sc.otp, sc.course_id, c.course_name,
               sc.hole_1, sc.hole_2, sc.hole_3, sc.hole_4, sc.hole_5, sc.hole_6,
               sc.hole_7, sc.hole_8, sc.hole_9, sc.hole_10, sc.hole_11, sc.hole_12,
               sc.hole_13, sc.hole_14, sc.hole_15, sc.hole_16, sc.hole_17, sc.hole_18
        FROM scorecard_configuration sc
        LEFT JOIN course_info c ON sc.course_id = c.course_id
        WHERE sc.event_mgm_id = ? AND sc.status = 0
        ORDER BY sc.course_id, sc.name";
$stmt_scorecard = $conn->prepare($scorecard_query);
$stmt_scorecard->bind_param("s", $event_mgm_id);
$stmt_scorecard->execute();
$scorecard_result = $stmt_scorecard->get_result();

$scorecard_configs = [];
while ($row = $scorecard_result->fetch_assoc()) {
    $scorecard_configs[] = $row;
}
$stmt_scorecard->close();

if (empty($scorecard_configs)) {
    die('No active scorecard configurations found for this event.');
}

// --- Fetch Flight Data ---
$flight_query = "
    SELECT fl.flight_name, fl.tee_box, fl.flight_time, fl.flight_date,
           rf.fullname, rf.nationality, rf.handicap,
           ec.category_name, fl.player_number
    FROM flight_list fl
    LEFT JOIN registration_form rf ON fl.form_id = rf.form_id
    LEFT JOIN event_category ec ON rf.category_id = ec.category_id
    WHERE fl.event_id IN ($placeholders)
    AND fl.round_number = ?
    AND fl.flight_list_name = ?
    ORDER BY fl.tee_box, fl.flight_name, fl.player_number";

$stmt_flight = $conn->prepare($flight_query);
$types = str_repeat('s', count($event_ids)) . 'is';
$params = array_merge($event_ids, [$round_number, $flight_list_name]);
$stmt_flight->bind_param($types, ...$params);
$stmt_flight->execute();
$flight_result = $stmt_flight->get_result();

$flight_data = [];
while ($row = $flight_result->fetch_assoc()) {
    $flight_data[] = $row;
}
$stmt_flight->close();

if (empty($flight_data)) {
    die('No flight data found for the given parameters.');
}

// Group flights by tee box
$flights_by_tee = [];
foreach ($flight_data as $flight) {
    $tee_box = $flight['tee_box'];
    if (!isset($flights_by_tee[$tee_box])) {
        $flights_by_tee[$tee_box] = [];
    }
    $flights_by_tee[$tee_box][] = $flight;
}

// Get flight date for header
$flight_date = $flight_data[0]['flight_date'] ?? date('Y-m-d');

    // Create Excel file
    try {
        $spreadsheet = new Spreadsheet();
        
        // Remove the default sheet
        $spreadsheet->removeSheetByIndex(0);
        
        // For each scorecard configuration, create a separate sheet
        foreach ($scorecard_configs as $config) {
            $station_name = $config['name'];
            $otp = $config['otp'];
            
            // Get selected holes for this configuration
            $selected_holes = [];
            for ($i = 1; $i <= 18; $i++) {
                if (isset($config["hole_$i"]) && $config["hole_$i"] == 1) {
                    $selected_holes[] = $i;
                }
            }
            
            if (empty($selected_holes)) {
                continue; // Skip if no holes selected
            }

            // Create new worksheet
            $sheet = $spreadsheet->createSheet();
            $sheet->setTitle($station_name);
            
            // Add title header spanning full width (A to K)
            $sheet->mergeCells('A1:K1');
            $sheet->setCellValue('A1', 'Live Scorer - ' . $event_name . ' (Final Round)');
            $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
            $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

            // Add date and course info spanning full width (A to K)
            $sheet->mergeCells('A2:K2');
            $sheet->setCellValue('A2', 'Date: ' . date('jS F Y, l', strtotime($flight_date)));
            $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
            $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            
            $rowNum = 3;
            
            // Get players grouped by tee box and flight name
            $player_query = "
                SELECT fl.flight_name, fl.tee_box, TIME_FORMAT(fl.flight_time, '%h:%i %p') as flight_time, rf.fullname, rf.nationality, ec.category_name
                FROM flight_list fl
                LEFT JOIN registration_form rf ON fl.form_id = rf.form_id
                LEFT JOIN event_category ec ON rf.category_id = ec.category_id
                WHERE fl.event_id IN ($placeholders)
                AND fl.round_number = ?
                AND fl.flight_list_name = ?
                AND rf.fullname IS NOT NULL
                ORDER BY fl.tee_box, fl.flight_name, fl.player_number";

            $stmt_player = $conn->prepare($player_query);
            $types = str_repeat('s', count($event_ids)) . 'is';
            $params = array_merge($event_ids, [$round_number, $flight_list_name]);
            $stmt_player->bind_param($types, ...$params);
            $stmt_player->execute();
            $player_result = $stmt_player->get_result();

            // Group players by tee box first, then by flight name
            $players_by_tee = [];
            while ($player = $player_result->fetch_assoc()) {
                $tee_box = $player['tee_box'];
                $flight_name = $player['flight_name'];
                $flight_time = $player['flight_time'];

                if (!isset($players_by_tee[$tee_box])) {
                    $players_by_tee[$tee_box] = [];
                }

                if (!isset($players_by_tee[$tee_box][$flight_name])) {
                    $players_by_tee[$tee_box][$flight_name] = [
                        'flight_time' => $flight_time,
                        'players' => []
                    ];
                }

                $players_by_tee[$tee_box][$flight_name]['players'][] = $player;
            }
            $stmt_player->close();

            // Sort tee boxes naturally
            uksort($players_by_tee, function($a, $b) {
                $numA = (int)preg_replace('/[^\d]/', '', $a);
                $numB = (int)preg_replace('/[^\d]/', '', $b);
                return $numA - $numB;
            });
            
            // Calculate column positions
            $left_cols = count($selected_holes) + 3; // Code + Player Name + CAT + holes
            $right_start_col = Coordinate::stringFromColumnIndex($left_cols + 2); // Leave 2 columns gap

            // Separate tees into left and right sides
            $left_tees = [];
            $right_tees = [];

            foreach ($players_by_tee as $tee_box => $flights) {
                $tee_num = (int)preg_replace('/[^\d]/', '', $tee_box);
                if ($tee_num <= 9) {
                    $left_tees[$tee_box] = $flights;
                } else {
                    $right_tees[$tee_box] = $flights;
                }
            }

            $left_row = $rowNum;
            $right_row = $rowNum;

            // Process left side tees (Tee 1-9)
            foreach ($left_tees as $tee_box => $flights) {
                $tee_num = (int)preg_replace('/[^\d]/', '', $tee_box);

                // Add tee header
                $start_col = 'A';
                $end_col = Coordinate::stringFromColumnIndex($left_cols);
                $sheet->mergeCells($start_col . $left_row . ':' . $end_col . $left_row);
                $sheet->setCellValue($start_col . $left_row, 'Tee ' . $tee_num);
                $sheet->getStyle($start_col . $left_row)->getFont()->setBold(true)->setSize(12);
                $sheet->getStyle($start_col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                $left_row++;

                // Sort flights by numeric order
                uksort($flights, function($a, $b) {
                    preg_match('/(\d+)/', $a, $matchesA);
                    preg_match('/(\d+)/', $b, $matchesB);
                    $numA = isset($matchesA[1]) ? (int)$matchesA[1] : 0;
                    $numB = isset($matchesB[1]) ? (int)$matchesB[1] : 0;
                    return $numA - $numB;
                });

                foreach ($flights as $flight_name => $group_data) {
                    $players = $group_data['players'];
                    $flight_time = $group_data['flight_time'];

                    // Add group header
                    $start_col = 'A';
                    $end_col = Coordinate::stringFromColumnIndex($left_cols);
                    $sheet->mergeCells($start_col . $left_row . ':' . $end_col . $left_row);
                    $sheet->setCellValue($start_col . $left_row, $flight_name . ' - ' . $flight_time);
                    $sheet->getStyle($start_col . $left_row)->getFont()->setBold(true)->setSize(10);
                    $sheet->getStyle($start_col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                    $left_row++;

                    // Add headers
                    $headers = ['Code', 'Player Name', 'CAT'];
                    foreach ($selected_holes as $hole) {
                        $headers[] = $hole;
                    }

                    $col = $start_col;
                    foreach ($headers as $header) {
                        $sheet->setCellValue($col . $left_row, $header);
                        $sheet->getStyle($col . $left_row)->getFont()->setBold(true);
                        $sheet->getStyle($col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;
                    }
                    $left_row++;

                    // Add player rows
                    foreach ($players as $player) {
                        $col = $start_col;

                        // Generate code
                        $fullname = $player['fullname'];
                        $name_parts = explode(' ', trim($fullname));
                        $code = '';
                        if (count($name_parts) >= 2) {
                            $code = strtoupper(substr($name_parts[0], 0, 1) . substr($name_parts[count($name_parts) - 1], 0, 1));
                        } else {
                            $code = strtoupper(substr($fullname, 0, 2));
                        }

                        $sheet->setCellValue($col . $left_row, $code);
                        $sheet->getStyle($col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;

                        // Player name
                        $sheet->setCellValue($col . $left_row, $fullname);
                        $col++;

                        // Category
                        $category = $player['category_name'] ?? '';
                        $sheet->setCellValue($col . $left_row, $category);
                        $sheet->getStyle($col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;

                        // Score columns
                        foreach ($selected_holes as $hole) {
                            $sheet->setCellValue($col . $left_row, '');
                            $sheet->getStyle($col . $left_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                            $col++;
                        }

                        $left_row++;
                    }

                    $left_row++; // Space between groups
                }

                $left_row++; // Space between tees
            }

            // Process right side tees (Tee 10-18)
            foreach ($right_tees as $tee_box => $flights) {
                $tee_num = (int)preg_replace('/[^\d]/', '', $tee_box);

                // Add tee header
                $start_col = $right_start_col;
                $end_col = Coordinate::stringFromColumnIndex(Coordinate::columnIndexFromString($start_col) + $left_cols - 1);
                $sheet->mergeCells($start_col . $right_row . ':' . $end_col . $right_row);
                $sheet->setCellValue($start_col . $right_row, 'Tee ' . $tee_num);
                $sheet->getStyle($start_col . $right_row)->getFont()->setBold(true)->setSize(12);
                $sheet->getStyle($start_col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                $right_row++;

                // Sort flights by numeric order
                uksort($flights, function($a, $b) {
                    preg_match('/(\d+)/', $a, $matchesA);
                    preg_match('/(\d+)/', $b, $matchesB);
                    $numA = isset($matchesA[1]) ? (int)$matchesA[1] : 0;
                    $numB = isset($matchesB[1]) ? (int)$matchesB[1] : 0;
                    return $numA - $numB;
                });

                foreach ($flights as $flight_name => $group_data) {
                    $players = $group_data['players'];
                    $flight_time = $group_data['flight_time'];

                    // Add group header
                    $sheet->mergeCells($start_col . $right_row . ':' . $end_col . $right_row);
                    $sheet->setCellValue($start_col . $right_row, $flight_name . ' - ' . $flight_time);
                    $sheet->getStyle($start_col . $right_row)->getFont()->setBold(true)->setSize(10);
                    $sheet->getStyle($start_col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                    $right_row++;

                    // Add headers
                    $headers = ['Code', 'Player Name', 'CAT'];
                    foreach ($selected_holes as $hole) {
                        $headers[] = $hole;
                    }

                    $col = $start_col;
                    foreach ($headers as $header) {
                        $sheet->setCellValue($col . $right_row, $header);
                        $sheet->getStyle($col . $right_row)->getFont()->setBold(true);
                        $sheet->getStyle($col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;
                    }
                    $right_row++;

                    // Add player rows
                    foreach ($players as $player) {
                        $col = $start_col;

                        // Generate code
                        $fullname = $player['fullname'];
                        $name_parts = explode(' ', trim($fullname));
                        $code = '';
                        if (count($name_parts) >= 2) {
                            $code = strtoupper(substr($name_parts[0], 0, 1) . substr($name_parts[count($name_parts) - 1], 0, 1));
                        } else {
                            $code = strtoupper(substr($fullname, 0, 2));
                        }

                        $sheet->setCellValue($col . $right_row, $code);
                        $sheet->getStyle($col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;

                        // Player name
                        $sheet->setCellValue($col . $right_row, $fullname);
                        $col++;

                        // Category
                        $category = $player['category_name'] ?? '';
                        $sheet->setCellValue($col . $right_row, $category);
                        $sheet->getStyle($col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                        $col++;

                        // Score columns
                        foreach ($selected_holes as $hole) {
                            $sheet->setCellValue($col . $right_row, '');
                            $sheet->getStyle($col . $right_row)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                            $col++;
                        }

                        $right_row++;
                    }

                    $right_row++; // Space between groups
                }

                $right_row++; // Space between tees
            }
            
            // Auto-size columns for both sides
            $left_end_col = Coordinate::stringFromColumnIndex($left_cols);
            $right_end_col = Coordinate::stringFromColumnIndex(Coordinate::columnIndexFromString($right_start_col) + $left_cols - 1);

            // Auto-size left side columns
            foreach (range('A', $left_end_col) as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Auto-size right side columns if there's right side data
            if (!empty($right_tees)) {
                foreach (range($right_start_col, $right_end_col) as $col) {
                    $sheet->getColumnDimension($col)->setAutoSize(true);
                }
            }

            // Add borders for both sides (excluding header rows 1 and 2)
            $final_row = max($left_row, $right_row) - 1;
            if ($final_row > 2) {
                // Left side borders (starting from row 3)
                if (!empty($left_tees)) {
                    $sheet->getStyle('A3:' . $left_end_col . $final_row)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
                }

                // Right side borders (starting from row 3)
                if (!empty($right_tees)) {
                    $sheet->getStyle($right_start_col . '3:' . $right_end_col . $final_row)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
                }
            }
        }
        
        // Set the first sheet as active
        if ($spreadsheet->getSheetCount() > 0) {
            $spreadsheet->setActiveSheetIndex(0);
        }

    // Output as XLSX
    $filename = 'Station_Scorers_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $flight_list_name) . '_round_' . $round_number . '_' . date('Ymd_His') . '.xlsx';
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;

} catch (Exception $e) {
    error_log("Error generating Station Scorers XLSX: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'error' => 'Failed to generate Station Scorers XLSX file',
        'message' => $e->getMessage()
    ]);
}

exit();
?> 
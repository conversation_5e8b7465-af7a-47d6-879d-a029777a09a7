<?php
require("database.php");

header('Content-Type: application/json');

// Get parameters
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$round_number = isset($_GET['round_number']) ? $_GET['round_number'] : '';
$flight_list_name = isset($_GET['flight_list_name']) ? $_GET['flight_list_name'] : '';

if (!$event_mgm_id || !$round_number || !$flight_list_name) {
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

try {
    // Get event_ids associated with event_mgm_id
    $event_query = "
        SELECT event_id 
        FROM event_mgm 
        WHERE event_mgm_id = ?";
    $stmt_event = $conn->prepare($event_query);
    $stmt_event->bind_param("s", $event_mgm_id);
    $stmt_event->execute();
    $event_result = $stmt_event->get_result();

    $event_ids = [];
    while ($row = $event_result->fetch_assoc()) {
        $event_ids[] = $row['event_id'];
    }
    $stmt_event->close();

    if (empty($event_ids)) {
        echo json_encode(['success' => false, 'error' => 'No events found']);
        exit;
    }

    // Create placeholders for IN clause
    $placeholders = str_repeat('?,', count($event_ids) - 1) . '?';

    // Fetch Flight Data with current scores
    $flight_query = "
        SELECT fl.flight_name, fl.tee_box, fl.flight_time, fl.flight_date,
               rf.fullname, rf.nationality, rf.handicap, rf.form_id,
               ec.category_name, fl.player_number
        FROM flight_list fl
        LEFT JOIN registration_form rf ON fl.form_id = rf.form_id
        LEFT JOIN event_category ec ON rf.category_id = ec.category_id
        WHERE fl.event_id IN ($placeholders)
        AND fl.round_number = ?
        AND fl.flight_list_name = ?
        ORDER BY fl.tee_box, fl.flight_name, fl.player_number";

    $stmt_flight = $conn->prepare($flight_query);
    $types = str_repeat('s', count($event_ids)) . 'is';
    $params = array_merge($event_ids, [$round_number, $flight_list_name]);
    $stmt_flight->bind_param($types, ...$params);
    $stmt_flight->execute();
    $flight_result = $stmt_flight->get_result();

    $flight_data = [];
    $player_form_ids = [];
    
    while ($row = $flight_result->fetch_assoc()) {
        $flight_data[] = $row;
        if ($row['form_id']) {
            $player_form_ids[] = $row['form_id'];
        }
    }
    $stmt_flight->close();

    // Fetch current scores for all players
    $scores_data = [];
    if (!empty($player_form_ids)) {
        $form_placeholders = str_repeat('?,', count($player_form_ids) - 1) . '?';
        $event_placeholders = str_repeat('?,', count($event_ids) - 1) . '?';
        
        $scores_query = "
            SELECT s.form_id, ch.hole_number, s.strokes
            FROM scorecard s
            JOIN custom_holes ch ON s.custom_hole_id = ch.custom_hole_id
            WHERE s.form_id IN ($form_placeholders)
            AND s.event_id IN ($event_placeholders)
            AND s.round = ?
            AND s.status = 0";
        
        $stmt_scores = $conn->prepare($scores_query);
        $score_types = str_repeat('s', count($player_form_ids)) . str_repeat('s', count($event_ids)) . 'i';
        $score_params = array_merge($player_form_ids, $event_ids, [$round_number]);
        $stmt_scores->bind_param($score_types, ...$score_params);
        $stmt_scores->execute();
        $scores_result = $stmt_scores->get_result();
        
        while ($score_row = $scores_result->fetch_assoc()) {
            $form_id = $score_row['form_id'];
            $hole_number = $score_row['hole_number'];
            $strokes = $score_row['strokes'];
            
            if (!isset($scores_data[$form_id])) {
                $scores_data[$form_id] = [];
            }
            $scores_data[$form_id][$hole_number] = $strokes;
        }
        $stmt_scores->close();
    }

    // Add scores to flight data
    foreach ($flight_data as &$flight) {
        $form_id = $flight['form_id'];
        $flight['scores'] = isset($scores_data[$form_id]) ? $scores_data[$form_id] : [];
    }

    echo json_encode([
        'success' => true,
        'data' => $flight_data,
        'timestamp' => time()
    ]);

} catch (Exception $e) {
    error_log("Error in fetch_live_scorer_data.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Database error occurred'
    ]);
}
?>

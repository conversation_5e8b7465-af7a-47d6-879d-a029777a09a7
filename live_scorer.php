<?php
require("database.php");

// Get parameters
$event_mgm_id = isset($_GET['event_mgm_id']) ? $_GET['event_mgm_id'] : '';
$round_number = isset($_GET['round_number']) ? $_GET['round_number'] : '';
$flight_list_name = isset($_GET['flight_list_name']) ? $_GET['flight_list_name'] : '';

if (!$event_mgm_id || !$round_number || !$flight_list_name) {
    die('Missing required parameters');
}

// Fetch Event Name and Date
$event_info_query = "
    SELECT event_name, event_end_date
    FROM event_mgm
    WHERE event_mgm_id = ?
    LIMIT 1";
$stmt_event_info = $conn->prepare($event_info_query);
$stmt_event_info->bind_param("s", $event_mgm_id);
$stmt_event_info->execute();
$event_info_result = $stmt_event_info->get_result();
$event_info_row = $event_info_result->fetch_assoc();
$event_name = $event_info_row['event_name'] ?? 'Unknown Event';
$event_end_date = $event_info_row['event_end_date'] ?? null;
$stmt_event_info->close();

// Get event_ids associated with event_mgm_id
$event_query = "
    SELECT event_id 
    FROM event_mgm 
    WHERE event_mgm_id = ?";
$stmt_event = $conn->prepare($event_query);
$stmt_event->bind_param("s", $event_mgm_id);
$stmt_event->execute();
$event_result = $stmt_event->get_result();

$event_ids = [];
while ($row = $event_result->fetch_assoc()) {
    $event_ids[] = $row['event_id'];
}
$stmt_event->close();

if (empty($event_ids)) {
    die('No events found for this event_mgm_id');
}

// Create placeholders for IN clause
$placeholders = str_repeat('?,', count($event_ids) - 1) . '?';

// Fetch Flight Data with scores
$flight_query = "
    SELECT fl.flight_name, fl.tee_box, fl.flight_time, fl.flight_date,
           rf.fullname, rf.nationality, rf.handicap, rf.form_id,
           ec.category_name, fl.player_number
    FROM flight_list fl
    LEFT JOIN registration_form rf ON fl.form_id = rf.form_id
    LEFT JOIN event_category ec ON rf.category_id = ec.category_id
    WHERE fl.event_id IN ($placeholders)
    AND fl.round_number = ?
    AND fl.flight_list_name = ?
    ORDER BY fl.tee_box, fl.flight_name, fl.player_number";

$stmt_flight = $conn->prepare($flight_query);
$types = str_repeat('s', count($event_ids)) . 'is';
$params = array_merge($event_ids, [$round_number, $flight_list_name]);
$stmt_flight->bind_param($types, ...$params);
$stmt_flight->execute();
$flight_result = $stmt_flight->get_result();

$flight_data = [];
while ($row = $flight_result->fetch_assoc()) {
    $flight_data[] = $row;
}
$stmt_flight->close();

if (empty($flight_data)) {
    die('No flight data found for the given parameters.');
}

// Group flights by tee box and flight name
$flights_by_tee = [];
foreach ($flight_data as $flight) {
    $tee_box = $flight['tee_box'];
    $flight_name = $flight['flight_name'];
    if (!isset($flights_by_tee[$tee_box])) {
        $flights_by_tee[$tee_box] = [];
    }
    if (!isset($flights_by_tee[$tee_box][$flight_name])) {
        $flights_by_tee[$tee_box][$flight_name] = [
            'flight_time' => $flight['flight_time'],
            'players' => []
        ];
    }
    $flights_by_tee[$tee_box][$flight_name]['players'][] = $flight;
}

// Get flight date for header
$flight_date = $flight_data[0]['flight_date'] ?? date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Scorer - <?php echo htmlspecialchars($event_name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .live-scorer-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header-section {
            background: #ffffff;
            color: #000;
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #000;
        }

        .header-section h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }

        .header-section .date {
            margin: 5px 0 0 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .tee-section {
            margin: 20px;
        }
        
        .tee-groups {
            display: flex;
            gap: 20px;
            flex-wrap: nowrap;
            justify-content: flex-start;
            align-items: flex-start;
        }

        .tee-group {
            flex: 1;
            min-width: 400px;
            max-width: 600px;
        }
        
        .group-header {
            background: #000;
            color: white;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 0;
        }

        .flight-header {
            background: #666;
            color: white;
            padding: 6px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 0;
        }

        .flight-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            border: 1px solid #000;
        }

        .flight-table th {
            background: #f0f0f0;
            padding: 6px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #000;
            font-size: 12px;
        }

        .flight-table td {
            padding: 6px;
            border: 1px solid #000;
            text-align: center;
            font-size: 12px;
        }

        .flight-table tbody tr:nth-child(even) {
            background: #ffffff;
        }

        .flight-table tbody tr:nth-child(odd) {
            background: #ffffff;
        }
        
        .player-code {
            font-weight: bold;
            text-align: center;
            width: 50px;
            font-size: 11px;
        }

        .player-name {
            min-width: 180px;
            text-align: left;
            font-size: 11px;
        }

        .score-cell {
            width: 30px;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
        }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        @media (max-width: 1200px) {
            .tee-groups {
                flex-wrap: wrap;
            }

            .tee-group {
                min-width: 45%;
                max-width: 48%;
            }
        }

        @media (max-width: 768px) {
            .tee-groups {
                flex-direction: column;
                flex-wrap: nowrap;
            }

            .tee-group {
                min-width: 100%;
                max-width: 100%;
            }

            .flight-table {
                font-size: 14px;
            }

            .flight-table th,
            .flight-table td {
                padding: 6px;
            }
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        <span id="refresh-status">Auto-refresh: ON</span>
    </div>
    
    <div class="live-scorer-container">
        <div class="header-section">
            <h1>Live Scorer - <?php echo htmlspecialchars($event_name); ?> (Round <?php echo htmlspecialchars($round_number); ?>)</h1>
            <div class="date">Date: <?php echo date('jS F Y, l', strtotime($flight_date)); ?></div>
        </div>
        
        <div class="tee-section">
            <div class="tee-groups" id="live-scorer-content">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        let autoRefreshInterval;
        let isRefreshing = false;

        function generatePlayerCode(fullname) {
            const nameParts = fullname.trim().split(' ');
            let code = '';
            if (nameParts.length >= 2) {
                code = (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
            } else {
                code = fullname.substring(0, 2).toUpperCase();
            }
            return code;
        }

        function formatTime(timeString) {
            if (!timeString) return '';
            const time = new Date('1970-01-01T' + timeString + 'Z');
            return time.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        }

        function loadLiveScorerData() {
            if (isRefreshing) return;
            isRefreshing = true;

            $.ajax({
                url: 'fetch_live_scorer_data.php',
                type: 'GET',
                data: {
                    event_mgm_id: '<?php echo $event_mgm_id; ?>',
                    round_number: '<?php echo $round_number; ?>',
                    flight_list_name: '<?php echo $flight_list_name; ?>'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        renderLiveScorerData(response.data);
                        $('#refresh-status').text('Auto-refresh: ON - Last updated: ' + new Date().toLocaleTimeString());
                    } else {
                        console.error('Error:', response.error);
                        // Fall back to static data if AJAX fails
                        const staticData = <?php echo json_encode($flight_data); ?>;
                        renderLiveScorerData(staticData);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    // Fall back to static data if AJAX fails
                    const staticData = <?php echo json_encode($flight_data); ?>;
                    renderLiveScorerData(staticData);
                },
                complete: function() {
                    isRefreshing = false;
                }
            });
        }

        function renderLiveScorerData(data) {
            const container = $('#live-scorer-content');
            container.empty();

            // Group data by tee box first, then by flight and time
            const teeBoxes = {};
            data.forEach(flight => {
                const teeBox = flight.tee_box;
                if (!teeBoxes[teeBox]) {
                    teeBoxes[teeBox] = [];
                }

                // Find existing flight group or create new one
                let existingGroup = teeBoxes[teeBox].find(group =>
                    group.flight_name === flight.flight_name &&
                    group.flight_time === flight.flight_time
                );

                if (!existingGroup) {
                    existingGroup = {
                        flight_name: flight.flight_name,
                        flight_time: flight.flight_time,
                        players: []
                    };
                    teeBoxes[teeBox].push(existingGroup);
                }

                existingGroup.players.push(flight);
            });

            // Sort tee boxes naturally (Tee 1, Tee 2, ..., Tee 10, etc.)
            const sortedTeeBoxes = Object.keys(teeBoxes).sort((a, b) => {
                const numA = parseInt(a.replace(/\D/g, '')) || 0;
                const numB = parseInt(b.replace(/\D/g, '')) || 0;
                return numA - numB;
            });

            sortedTeeBoxes.forEach(teeBox => {
                const teeGroup = $('<div class="tee-group"></div>');
                teeGroup.append(`<div class="group-header">${teeBox}</div>`);

                // Sort flights by time within each tee box
                const flights = teeBoxes[teeBox];
                flights.sort((a, b) => {
                    if (a.flight_time && b.flight_time) {
                        return a.flight_time.localeCompare(b.flight_time);
                    }
                    return a.flight_name.localeCompare(b.flight_name);
                });

                flights.forEach(flightData => {
                    const flightTime = formatTime(flightData.flight_time);

                    teeGroup.append(`<div class="flight-header">${flightData.flight_name} (${teeBox} - ${flightTime})</div>`);

                    const table = $(`
                        <table class="flight-table">
                            <thead>
                                <tr>
                                    <th class="player-code">Code</th>
                                    <th class="player-name">Player Name</th>
                                    <th class="score-cell">1</th>
                                    <th class="score-cell">2</th>
                                    <th class="score-cell">3</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    `);

                    const tbody = table.find('tbody');
                    flightData.players.forEach(player => {
                        const code = generatePlayerCode(player.fullname);

                        // Get scores for holes 1, 2, 3
                        const scores = player.scores || {};
                        const hole1Score = scores[1] ? scores[1] : '';
                        const hole2Score = scores[2] ? scores[2] : '';
                        const hole3Score = scores[3] ? scores[3] : '';

                        const row = $(`
                            <tr>
                                <td class="player-code">${code}</td>
                                <td class="player-name">${player.fullname}</td>
                                <td class="score-cell">${hole1Score}</td>
                                <td class="score-cell">${hole2Score}</td>
                                <td class="score-cell">${hole3Score}</td>
                            </tr>
                        `);
                        tbody.append(row);
                    });

                    teeGroup.append(table);
                });

                container.append(teeGroup);
            });
        }

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(loadLiveScorerData, 15000); // Refresh every 15 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        $(document).ready(function() {
            // Initial load with static data
            const staticData = <?php echo json_encode($flight_data); ?>;
            renderLiveScorerData(staticData);

            // Start auto-refresh
            setTimeout(startAutoRefresh, 5000); // Start after 5 seconds
        });
    </script>
</body>
</html>
